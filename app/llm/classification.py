#!/usr/bin/env python3
"""
Production Document Classification Processor - S3 Only

This module processes single documents from S3 for production use:
1. Takes S3 URI as input (PDF, JPG, JPEG, PNG, TIF, TIFF)
2. For PDFs: Downloads, splits into individual pages, processes in parallel
3. For images: Processes directly from S3
4. Processes each page/image with AWS Textract (sync method) in parallel
5. Merges all page results with <page1></page1> tags
6. Uses AWS Bedrock Converse API with OpenAI models to analyze document types
7. Returns structured response objects for production integration

Production Features:
- S3-only processing (no local file support)
- Single file processing only
- No local file saving/loading
- AWS credentials from settings
- Returns textract, bedrock, and classification response objects
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

import os
import json
import logging
import tempfile
import concurrent.futures
import time
import uuid
import hashlib
from pathlib import Path
from typing import List, Dict, Tuple, Optional

import boto3
import PyPDF2
from botocore.exceptions import ClientError

# Import settings for AWS configuration
from app.core.configuration import settings


def setup_logging() -> logging.Logger:
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        force=True  # Force reconfiguration to ensure logs appear in terminal
    )
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    return logger


def is_supported_file_type(file_path: str) -> bool:
    """
    Check if the file type is supported for processing.

    Args:
        file_path: Path to the file

    Returns:
        True if file type is supported, False otherwise
    """
    supported_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.tif', '.tiff'}
    file_extension = Path(file_path).suffix.lower()
    return file_extension in supported_extensions


def is_pdf_file(file_path: str) -> bool:
    """
    Check if the file is a PDF file.

    Args:
        file_path: Path to the file

    Returns:
        True if file is a PDF, False otherwise
    """
    return Path(file_path).suffix.lower() == '.pdf'


def split_pdf(pdf_path: str, output_dir: str) -> List[str]:
    """
    Split PDF into individual pages and save them as separate PDF files.

    Args:
        pdf_path: Path to the input PDF file
        output_dir: Directory to save individual page PDFs

    Returns:
        List of paths to individual page PDF files
    """
    page_files = []

    # Create unique identifier for this PDF to avoid conflicts
    pdf_hash = hashlib.md5(pdf_path.encode()).hexdigest()[:8]
    pdf_name = Path(pdf_path).stem

    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        total_pages = len(pdf_reader.pages)

        logging.info(f"Splitting PDF {pdf_name} into {total_pages} pages")

        for page_num in range(total_pages):
            # Create a new PDF writer for each page
            pdf_writer = PyPDF2.PdfWriter()
            pdf_writer.add_page(pdf_reader.pages[page_num])

            # Save individual page with unique naming to prevent conflicts
            page_filename = f"{pdf_name}_{pdf_hash}_page_{page_num + 1:03d}.pdf"
            page_path = os.path.join(output_dir, page_filename)

            with open(page_path, 'wb') as output_file:
                pdf_writer.write(output_file)

            page_files.append(page_path)
            logging.debug(f"Created page file: {page_filename}")

    return page_files





def process_page_with_textract_response(page_path: str, s3_client, textract_client, bucket_name: str, temp_prefix: str) -> Tuple[int, str, Dict]:
    """
    Process a single PDF page with AWS Textract and return both text and response object.

    Args:
        page_path: Path to the PDF page file
        s3_client: AWS S3 client
        textract_client: AWS Textract client
        bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files

    Returns:
        Tuple of (page_number, extracted_text, textract_response_object)
    """
    try:
        # Extract page number from filename (handle new format: pdfname_hash_page_001.pdf)
        page_filename = os.path.basename(page_path)

        # Parse filename to extract page number safely
        if '_page_' in page_filename:
            # New format: pdfname_hash_page_001.pdf
            page_part = page_filename.split('_page_')[1].split('.')[0]
            page_num = int(page_part)
        else:
            # Fallback for old format: page_001.pdf
            page_num = int(page_filename.split('_')[1].split('.')[0])

        logging.debug(f"Processing page {page_num} from file: {page_filename}")

        s3_key = None  # Track if we uploaded to S3

        try:
            # Try sync method first (faster for single pages) - use direct file input
            with open(page_path, 'rb') as document_file:
                response = textract_client.detect_document_text(
                    Document={'Bytes': document_file.read()}
                )
            logging.debug(f"Sync method succeeded for page {page_num}")

        except Exception as sync_error:
            logging.warning(f"Sync method failed for page {page_num}, trying async method: {sync_error}")

            # Upload to S3 only when async method is needed with unique key
            timestamp = int(time.time() * 1000)  # millisecond timestamp
            unique_id = uuid.uuid4().hex[:8]
            s3_key = f"{temp_prefix}/{timestamp}_{unique_id}_{page_filename}"
            s3_client.upload_file(page_path, bucket_name, s3_key)
            logging.info(f"Uploaded {page_filename} to S3 with unique key: {s3_key}")

            # Fallback to async method
            try:
                # Start async job
                start_response = textract_client.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': bucket_name,
                            'Name': s3_key
                        }
                    }
                )

                job_id = start_response['JobId']
                logging.info(f"Started async job {job_id} for page {page_num}")

                # Poll for completion
                max_wait_time = 300  # 5 minutes max wait
                poll_interval = 2    # Poll every 2 seconds
                elapsed_time = 0

                while elapsed_time < max_wait_time:
                    time.sleep(poll_interval)
                    elapsed_time += poll_interval

                    result_response = textract_client.get_document_text_detection(JobId=job_id)
                    status = result_response['JobStatus']

                    if status == 'SUCCEEDED':
                        response = result_response
                        logging.info(f"Async method succeeded for page {page_num}")
                        break
                    elif status == 'FAILED':
                        raise Exception(f"Async job failed: {result_response.get('StatusMessage', 'Unknown error')}")
                    elif status in ['IN_PROGRESS']:
                        logging.debug(f"Async job {job_id} still in progress...")
                        continue
                    else:
                        raise Exception(f"Unexpected job status: {status}")

                if elapsed_time >= max_wait_time:
                    raise Exception(f"Async job timed out after {max_wait_time} seconds")

            except Exception as async_error:
                logging.error(f"Both sync and async methods failed for page {page_num}: {async_error}")
                raise async_error

        # Extract text from response
        text_lines = []
        for block in response.get('Blocks', []):
            if block['BlockType'] == 'LINE':
                text_lines.append(block.get('Text', ''))

        extracted_text = '\n'.join(text_lines)

        # Add validation and metadata for debugging
        char_count = len(extracted_text)
        line_count = len(text_lines)

        logging.info(f"Page {page_num}: Extracted {char_count} characters, {line_count} lines from {page_filename}")

        validated_text = f"{extracted_text}"

        # Clean up S3 file only if it was uploaded
        if s3_key:
            try:
                s3_client.delete_object(Bucket=bucket_name, Key=s3_key)
                logging.debug(f"Cleaned up S3 file: {s3_key}")
            except Exception:
                pass  # Ignore cleanup errors

        return page_num, validated_text, response

    except Exception as e:
        logging.error(f"Error processing page {page_path}: {e}")
        return 0, f"Error processing page: {e}", {}





def process_s3_image_with_textract_response(bucket_name: str, object_key: str, aws_access_key_id: Optional[str], aws_secret_access_key: Optional[str], aws_region: str) -> Tuple[str, Dict]:
    """
    Process a single image file from S3 with AWS Textract and return both text and response object.

    Args:
        bucket_name: S3 bucket name
        object_key: S3 object key
        aws_access_key_id: AWS access key ID
        aws_secret_access_key: AWS secret access key
        aws_region: AWS region

    Returns:
        Tuple of (extracted_text, textract_response_object)
    """
    try:
        logging.debug(f"Processing S3 image: s3://{bucket_name}/{object_key}")

        # Initialize Textract client
        textract_client = boto3.client(
            'textract',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=aws_region
        )

        try:
            # Try sync method first - use S3 document location
            response = textract_client.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_key
                    }
                }
            )
            logging.debug(f"Sync method succeeded for S3 image {object_key}")

        except Exception as sync_error:
            logging.warning(f"Sync method failed for S3 image {object_key}, trying async method: {sync_error}")

            # Fallback to async method
            try:
                # Start async job
                start_response = textract_client.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': bucket_name,
                            'Name': object_key
                        }
                    }
                )

                job_id = start_response['JobId']
                logging.info(f"Started async job {job_id} for S3 image {object_key}")

                # Poll for completion
                max_wait_time = 300  # 5 minutes max wait
                poll_interval = 2    # Poll every 2 seconds
                elapsed_time = 0

                while elapsed_time < max_wait_time:
                    time.sleep(poll_interval)
                    elapsed_time += poll_interval

                    result_response = textract_client.get_document_text_detection(JobId=job_id)
                    status = result_response['JobStatus']

                    if status == 'SUCCEEDED':
                        response = result_response
                        logging.info(f"Async method succeeded for S3 image {object_key}")
                        break
                    elif status == 'FAILED':
                        raise Exception(f"Async job failed: {result_response.get('StatusMessage', 'Unknown error')}")
                    elif status in ['IN_PROGRESS']:
                        logging.debug(f"Async job {job_id} still in progress...")
                        continue
                    else:
                        raise Exception(f"Unexpected job status: {status}")

                if elapsed_time >= max_wait_time:
                    raise Exception(f"Async job timed out after {max_wait_time} seconds")

            except Exception as async_error:
                logging.error(f"Both sync and async methods failed for S3 image {object_key}: {async_error}")
                raise async_error

        # Extract text from response
        text_lines = []
        for block in response.get('Blocks', []):
            if block['BlockType'] == 'LINE':
                text_lines.append(block.get('Text', ''))

        extracted_text = '\n'.join(text_lines)

        # Add validation and metadata for debugging
        char_count = len(extracted_text)
        line_count = len(text_lines)

        logging.info(f"S3 Image {object_key}: Extracted {char_count} characters, {line_count} lines")

        return extracted_text, response

    except Exception as e:
        logging.error(f"Error processing S3 image s3://{bucket_name}/{object_key}: {e}")
        return f"Error processing S3 image: {e}", {}


def process_s3_pdf_with_responses(bucket_name: str, object_key: str, temp_bucket_name: str, temp_prefix: str, aws_access_key_id: Optional[str], aws_secret_access_key: Optional[str], aws_region: str) -> Tuple[str, List[Dict]]:
    """
    Process a PDF file from S3 by downloading, splitting, and processing in parallel.

    Args:
        bucket_name: S3 bucket name containing the PDF
        object_key: S3 object key for the PDF
        temp_bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files
        aws_access_key_id: AWS access key ID
        aws_secret_access_key: AWS secret access key
        aws_region: AWS region

    Returns:
        Tuple of (combined_text_with_page_tags, list_of_textract_response_objects)
    """
    try:
        logging.info(f"Processing S3 PDF: s3://{bucket_name}/{object_key}")

        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=aws_region
        )

        # Download PDF to temporary directory and process
        with tempfile.TemporaryDirectory() as temp_dir:
            # Download PDF from S3
            pdf_filename = os.path.basename(object_key)
            local_pdf_path = os.path.join(temp_dir, pdf_filename)

            logging.info(f"Downloading PDF from S3 to {local_pdf_path}")
            s3_client.download_file(bucket_name, object_key, local_pdf_path)

            # Split PDF into individual pages
            logging.info("Splitting PDF into individual pages...")
            page_files = split_pdf(local_pdf_path, temp_dir)
            logging.info(f"Split PDF into {len(page_files)} pages")

            # Process pages in parallel
            logging.info("Processing pages with Textract in parallel...")
            combined_text, textract_responses = process_pdf_pages_parallel_with_responses(
                page_files, temp_bucket_name, temp_prefix, aws_access_key_id, aws_secret_access_key, aws_region
            )

            # Validate and clean the extracted text
            combined_text = validate_and_clean_extracted_text(combined_text, local_pdf_path)

            return combined_text, textract_responses

    except Exception as e:
        logging.error(f"Error processing S3 PDF s3://{bucket_name}/{object_key}: {e}")
        raise


def process_pdf_pages_parallel_with_responses(page_files: List[str], bucket_name: str, temp_prefix: str, aws_access_key_id: Optional[str], aws_secret_access_key: Optional[str], aws_region: str, max_workers: int = 5) -> Tuple[str, List[Dict]]:
    """
    Process multiple PDF pages with Textract in parallel and return both text and response objects.

    Args:
        page_files: List of paths to individual page PDF files
        bucket_name: S3 bucket name for temporary uploads
        temp_prefix: S3 prefix for temporary files
        aws_access_key_id: AWS access key ID
        aws_secret_access_key: AWS secret access key
        aws_region: AWS region
        max_workers: Maximum number of parallel workers

    Returns:
        Tuple of (combined_text_with_page_tags, list_of_textract_response_objects)
    """
    # Create unique temp prefix for this processing session
    session_id = uuid.uuid4().hex[:8]
    unique_temp_prefix = f"{temp_prefix}-{session_id}"

    logging.info(f"Starting parallel processing of {len(page_files)} pages with session ID: {session_id}")

    # Initialize AWS clients with credentials
    s3_client = boto3.client(
        's3',
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=aws_region
    )
    textract_client = boto3.client(
        'textract',
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key,
        region_name=aws_region
    )

    # Process pages in parallel
    page_results = {}
    page_responses = {}
    expected_pages = set()

    # Extract expected page numbers from filenames
    for page_file in page_files:
        filename = os.path.basename(page_file)
        if '_page_' in filename:
            page_part = filename.split('_page_')[1].split('.')[0]
            expected_pages.add(int(page_part))
        else:
            page_part = filename.split('_')[1].split('.')[0]
            expected_pages.add(int(page_part))

    logging.info(f"Expected pages: {sorted(expected_pages)}")

    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_page = {
            executor.submit(process_page_with_textract_response, page_path, s3_client, textract_client, bucket_name, unique_temp_prefix): page_path
            for page_path in page_files
        }

        # Collect results
        for future in concurrent.futures.as_completed(future_to_page):
            page_path = future_to_page[future]
            try:
                page_num, extracted_text, textract_response = future.result()

                # Validate page number is expected
                if page_num not in expected_pages:
                    logging.warning(f"Unexpected page number {page_num} from {page_path}. Expected: {sorted(expected_pages)}")

                # Check for duplicate page results
                if page_num in page_results:
                    logging.error(f"Duplicate page {page_num} detected! Previous: {len(page_results[page_num])} chars, New: {len(extracted_text)} chars")
                    # Keep the longer text (likely more complete)
                    if len(extracted_text) > len(page_results[page_num]):
                        page_results[page_num] = extracted_text
                        page_responses[page_num] = textract_response
                        logging.info(f"Replaced page {page_num} with longer text")
                else:
                    page_results[page_num] = extracted_text
                    page_responses[page_num] = textract_response

                logging.info(f"Completed processing page {page_num} from {os.path.basename(page_path)}")
            except Exception as e:
                logging.error(f"Error processing {page_path}: {e}")

    # Validate all expected pages were processed
    processed_pages = set(page_results.keys()) - {0}  # Remove error pages
    missing_pages = expected_pages - processed_pages
    extra_pages = processed_pages - expected_pages

    if missing_pages:
        logging.error(f"Missing pages: {sorted(missing_pages)}")
    if extra_pages:
        logging.warning(f"Extra pages: {sorted(extra_pages)}")

    # Combine results in page order
    combined_text_parts = []
    textract_responses = []
    for page_num in sorted(page_results.keys()):
        if page_num > 0:  # Skip error pages (page_num = 0)
            page_text = page_results[page_num]
            combined_text_parts.append(f"<page{page_num}>\n{page_text}\n</page{page_num}>")
            if page_num in page_responses:
                textract_responses.append(page_responses[page_num])

    logging.info(f"Combined {len(combined_text_parts)} pages into final text")
    return '\n\n'.join(combined_text_parts), textract_responses


def validate_and_clean_extracted_text(combined_text: str, pdf_path: str) -> str:
    """
    Validate and clean the extracted text to ensure data integrity.

    Args:
        combined_text: Combined text from all pages
        pdf_path: Original PDF path for validation

    Returns:
        Cleaned and validated text
    """
    pdf_name = Path(pdf_path).stem

    # Split into pages
    pages = combined_text.split('<page')
    cleaned_pages = []

    for i, page_content in enumerate(pages):
        if not page_content.strip():
            continue

        # Extract page number and content
        if '>' in page_content:
            page_header, content = page_content.split('>', 1)
            if '</page' in content:
                content = content.split('</page')[0]

            # Validate page metadata if present
            if '[PAGE_METADATA:' in content:
                metadata_end = content.find(']')
                if metadata_end != -1:
                    metadata = content[:metadata_end + 1]

                    # Check if metadata matches expected PDF
                    if f'file={pdf_name}' in metadata or pdf_name in metadata:
                        cleaned_pages.append(f'<page{page_header}>\n{content}\n</page{page_header.split(">")[0]}>')
                        logging.debug(f"Validated page {i} metadata for {pdf_name}")
                    else:
                        logging.warning(f"Page {i} metadata mismatch for {pdf_name}: {metadata}")
                        # Still include but flag for review
                        cleaned_pages.append(f'<page{page_header}>\n[VALIDATION_WARNING: Metadata mismatch]\n{content}\n</page{page_header.split(">")[0]}>')
                else:
                    cleaned_pages.append(f'<page{page_header}>\n{content}\n</page{page_header.split(">")[0]}>')
            else:
                cleaned_pages.append(f'<page{page_header}>\n{content}\n</page{page_header.split(">")[0]}>')

    cleaned_text = '\n\n'.join(cleaned_pages)

    # Log validation summary
    original_pages = len([p for p in pages if p.strip()])
    cleaned_page_count = len(cleaned_pages)

    logging.info(f"Text validation for {pdf_name}: {original_pages} -> {cleaned_page_count} pages")

    return cleaned_text


def analyze_document_types_with_bedrock(combined_text: str, region: str = 'us-east-1') -> Tuple[Dict, Dict]:
    """
    Analyze document types using AWS Bedrock Converse API with OpenAI models.

    Args:
        combined_text: Combined text from all pages with page tags
        region: AWS region for Bedrock

    Returns:
        Tuple containing (analysis_result, full_bedrock_response)
    """
    # Use credentials from settings
    aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
    aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None

    bedrock_client = boto3.client(
        'bedrock-runtime',
        region_name=region,
        aws_access_key_id=aws_access_key_id,
        aws_secret_access_key=aws_secret_access_key
    )
    
    # System prompt for document type analysis
    system_prompt = '''
                ## Task Summary:
                    You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.

                ## Model Instructions:
                    - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.
                    - Use **only** the `classify_logistics_doc_type` tool to return results.
                    - For every page in the input PDF you MUST return exactly one object describing that page.
                    - Do NOT return any extra pages or skip pages. Output pages in ascending page order.
                    - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).
                    - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.
                    - Strickly check for contination of previous page by checking if the page starts with any of the following: "continued", "continued on next page", "continued on next", etc. or page 2 of 3, etc.

                ## Enum details for doc_type:

                    invoice — Carrier Invoice
                    Definition: Bill issued by a carrier for services rendered (generic carrier billing).
                    Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.
                    Key fields/structure: carrier billing address, shipment ID, line charges, total due.

                    bol — Bill of Lading
                    Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.
                    Keywords indication: "Bill of Lading", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading
                    Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.

                    pod — Proof of Delivery
                    Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.
                    Keywords indication: "Proof of Delivery", "Delivery Ticket", POD, Received by, Delivered, Delivery Receipt, Date Received
                    Key fields/structure: signature/date block, delivery address, condition remarks, signature line.

                    rate_confirmation — Carrier Rate Confirmation
                    Definition: Agreement from a carrier confirming rate/terms for a specific load.
                    Keywords indication: "Carrier Rate Confirmation", Carrier Rate, Rate Confirmation, Carrier:
                    Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.

                    cust_rate_confirmation — Customer Rate Confirmation
                    Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.
                    Keywords indication: "Customer Rate Confirmation", Customer Rate, Quote to
                    Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.

                    clear_to_pay — Clear to Pay
                    Definition: Authorization indicating invoice is approved for payment.
                    Keywords indication: "Clear to Pay", Approved for Payment, Payment Authorization, Clear to Pay Stamp
                    Key fields/structure: approval stamps, audit/verification notes, approver name/date.

                    scale_ticket — Scale Ticket
                    Definition: Weight record from a scale for vehicle/load (used for billing/compliance).
                    Keywords indication: "Scale Ticket", Gross Weight, Tare, Net Weight, Weighed At
                    Key fields/structure: gross/tare/net values, scale location, date/time, truck ID.

                    log — Log
                    Definition: Activity record (driver log, tracking log) for operational/regulatory use.
                    Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp
                    Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.

                    fuel_receipt — Fuel Receipt
                    Definition: Receipt for fuel purchase (expense item).
                    Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt
                    Key fields/structure: fuel quantity, unit price, station name/address, payment method.

                    combined_carrier_documents — Combined Carrier Documents
                    Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).
                    Keywords indication: multiple distinct document headers on one page, Bundle, Combined
                    Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.

                    pack_list — Packing List
                    Definition: Itemized list of shipment contents for inventory/receiving checks.
                    Keywords indication: Packing List, Pack List, Contents, Qty, Item Description
                    Key fields/structure: SKU/description rows, package counts, net/gross units.

                    po — Purchase Order
                    Definition: Buyer’s order to a seller specifying items, qty, and price.
                    Keywords indication: Purchase Order, PO#, Buyer, PO Number
                    Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.

                    comm_invoice — Commercial Invoice
                    Definition: Customs-focused invoice for international shipments (value, HS codes).
                    Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value
                    Key fields/structure: declared value, HS codes, importer/exporter details.

                    customs_doc — Customs Document, Certificate of Origin
                    Definition: General customs paperwork (declarations, certificates, permits).
                    Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker
                    Key fields/structure: declaration forms, license numbers

                    nmfc_cert — NMFC Certificate
                    Definition: Document showing National Motor Freight Classification codes/class assignments.
                    Keywords indication: NMFC, National Motor Freight Classification, Class
                    Key fields/structure: NMFC codes, class #, commodity description.

                    other — Other
                    Definition: Any logistics-related page that doesn’t fit the above categories. Use sparingly.
                    Keywords indication: none specific.
                    Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.

                    coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate
                    Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature
                    Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate
                    Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.

                    tender_from_cust — Load Tender from Customer
                    Definition: Customer’s load tender or request to a carrier to transport a load.
                    Keywords indication: Load Tender, Tender, Tender from, Request to Carrier
                    Key fields/structure: tender number, pickup/delivery instructions, special requirements.

                    lumper_receipt — Lumper Receipt
                    Definition: Receipt for lumper services (loading/unloading labor).
                    Keywords indication: Lumper, Lumper Receipt, Lumper Charge
                    Key fields/structure: labor charge, lumper name/signature, location/date.

                    so_confirmation — Sales Order Confirmation
                    Definition: Seller’s confirmation of a sales order (acknowledges order details).
                    Keywords indication: Sales Order Confirmation, SO#, Order Confirmation
                    Key fields/structure: SO number, confirmed quantities/dates/prices.

                    po_confirmation — Purchase Order Confirmation
                    Definition: Seller’s acceptance/confirmation of a buyer’s PO.
                    Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement
                    Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.

                    pre_pull_receipt — Pre-Pull Receipt
                    Definition: Proof/receipt for pre-pulling a container or load before scheduled pickup.
                    Keywords indication: Pre-pull, Pre Pull, Prepull, Pre-Pull Receipt
                    Key fields/structure: container ID, pull date/time, storage/yard reference, fees.

                    ingate — Ingate Document
                    Definition: Record of vehicle/container entering a facility (gate-in).
                    Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In
                    Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.

                    outgate — Outgate Document
                    Definition: Record of vehicle/container exiting a facility (gate-out/release).
                    Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time
                    Key fields/structure: release stamp, exit time, fees, container/truck number.
            '''

    # User message with the combined text
    user_message = f"Please analyze this multi-page document and classify each page:\n\n{combined_text}"
    
    # Prepare messages for Bedrock
    messages = [
        {
            "role": "user", 
            "content": [{"text": user_message}]
        }
    ]
    
    # Try models available on Bedrock (using inference profiles and available models)
    model_id = "amazon.nova-pro-v1:0"  # Use Nova Pro model

    # Define the tool configuration with enum and schema for page details
    tool_config = {
        "tools": [
            {
                "toolSpec": {
                    "name": "classify_logistics_doc_type",
                    "description": "Classify logistics document type from multi-page documents",
                    "inputSchema": {
                        "json": {
                            "type": "object",
                            "properties": {
                                "documents": {
                                    "type": "array",
                                    "description": "Array of extracted document type summaries from the multi-page document",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "page_no": {
                                                "type": "integer",
                                                "description": "Current page no for which the document type is mentioned"
                                            },
                                            "doc_type": {
                                                "type": "string",
                                                "enum": [
                                                    "invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation",
                                                    "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log",
                                                    "fuel_receipt",
                                                    "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other",
                                                    "coa", "lumper_receipt",
                                                    "so_confirmation", "po_confirmation", "pre_pull_receipt",  
                                                ],
                                                "description": "For detailed description of each enum, refer to the system prompt"
                                            }
                                        },
                                        "required": ["page_no", "doc_type"]
                                    }
                                }
                            },
                            "required": ["documents"]
                        }
                    }
                }
            }
        ]
}


    logging.info(f"Trying Bedrock model: {model_id}")
    response = bedrock_client.converse(
            modelId=model_id,
            system=[{"text": system_prompt}],
            messages=messages,
            toolConfig=tool_config,
            inferenceConfig={
                'temperature': 0.7
            }

        )


    # Extract response text
    content = response['output']['message']['content'][1]['toolUse']['input']
    return content, response


def process_document(file_path: str) -> Dict:
    """
    Production-ready function to process a single document file (PDF or image) from S3 with Textract and Bedrock.

    Args:
        file_path: S3 URI (s3://bucket/key) to the document file to process (PDF, JPG, JPEG, PNG, TIF, TIFF)

    Returns:
        Dictionary containing:
        - textract_responses: List of raw Textract response objects for each page
        - bedrock_response: Complete Bedrock response object
        - classification_result: Main response object with page_no and doc_type for each page
        - combined_text: Combined text from all pages with page tags
    """
    # Setup logging first
    logger = setup_logging()

    # Validate S3 URI format
    if not file_path.startswith('s3://'):
        raise ValueError(f"Only S3 URIs are supported. Expected format: s3://bucket/key, got: {file_path}")

    # Parse S3 URI
    s3_parts = file_path.replace('s3://', '').split('/', 1)
    if len(s3_parts) != 2:
        raise ValueError(f"Invalid S3 URI format: {file_path}. Expected format: s3://bucket/key")
    bucket_name, object_key = s3_parts

    # Validate file type from S3 object key
    if not is_supported_file_type(object_key):
        raise ValueError("S3 object must be a supported type (PDF, JPG, JPEG, PNG, TIF, TIFF)")

    # Determine file type
    file_type = "PDF" if is_pdf_file(object_key) else "Image"
    logger.info(f"Processing {file_type} from S3: {file_path}")

    # Configuration - use settings from environment
    TEMP_BUCKET_NAME = settings.S3_BUCKET_NAME or "document-extraction-logistically"
    TEMP_PREFIX = "temp-document-processing"

    # Initialize AWS clients with settings
    aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
    aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
    aws_region = settings.AWS_REGION

    textract_responses = []

    try:
        if is_pdf_file(object_key):
            # Process PDF from S3 - download, split, and process in parallel
            logger.info("Processing PDF from S3...")
            combined_text, textract_responses = process_s3_pdf_with_responses(
                bucket_name, object_key, TEMP_BUCKET_NAME, TEMP_PREFIX,
                aws_access_key_id, aws_secret_access_key, aws_region
            )
        else:
            # Process image from S3 directly
            logger.info("Processing image from S3...")
            extracted_text, textract_response = process_s3_image_with_textract_response(
                bucket_name, object_key, aws_access_key_id, aws_secret_access_key, aws_region
            )
            textract_responses = [textract_response]
            # For images, wrap in page1 tags to maintain consistency with PDF processing
            combined_text = f"<page1>\n{extracted_text}\n</page1>"

        logger.info("Analyzing document types with Bedrock...")
        classification_result, bedrock_response = analyze_document_types_with_bedrock(combined_text, aws_region)

        result = {
            'textract_responses': textract_responses,
            'bedrock_response': bedrock_response,
            'classification_result': classification_result,
            'combined_text': combined_text
        }

        logger.info(f"Successfully processed S3 document: {file_path}")
        return result

    except Exception as e:
        logger.error(f"Processing failed for {file_path}: {e}")
        raise


def main(file_path: str="s3://document-extraction-logistically/temp/classification_sample_2.pdf") -> Dict:
    """
    Main function for production use - processes a single document and returns structured response.

    Args:
        file_path: Path to the document file to process

    Returns:
        Dictionary containing textract_responses, bedrock_response, and classification_result
    """
    return process_document(file_path)


if __name__ == "__main__":


    result = main()
    print(json.dumps(result["classification_result"], indent=4))