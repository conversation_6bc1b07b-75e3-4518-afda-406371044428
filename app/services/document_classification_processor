"""
Async Worker to process S3 events from an SQS queue (classification pipeline).
"""
import sys
import json
import asyncio
from io import By<PERSON><PERSON>
from pathlib import Path
import os
from typing import Dict, Any
from pypdf import Pdf<PERSON><PERSON>er, PdfWriter
from io import BytesIO

import aioboto3
from botocore.exceptions import ClientError
from loguru import logger

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

from app.core.configuration import settings
from app.services.s3_service import s3_service
from app.llm.extraction import main as llm_main


class DocumentClassificationProcessor:
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL is required.")
        self.queue_url = queue_url
        self.region_name = region_name
        self.aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        self.aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
        self.output_folder = settings.CLASSIFICATION_FOLDER_OUTPUT_PREFIX
        logger.info(f"[Classification Worker] Initialized for queue: {queue_url}")

    async def process_messages(self):
        session = aioboto3.Session()
        async with session.client(
            "sqs",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.region_name,
        ) as sqs_client:
            while True:
                try:
                    response = await sqs_client.receive_message(
                        QueueUrl=self.queue_url,
                        MaxNumberOfMessages=10,
                        WaitTimeSeconds=20,
                        MessageAttributeNames=["All"],
                    )
                    messages = response.get("Messages", [])
                    if not messages:
                        logger.debug("[Classification Worker] No new messages.")
                        continue

                    logger.info(f"[Classification Worker] Got {len(messages)} messages.")
                    await asyncio.gather(
                        *[self._process_single_message(m, sqs_client) for m in messages]
                    )
                except ClientError as e:
                    logger.error(f"[Classification Worker] SQS error: {e}")
                    await asyncio.sleep(10)

    async def _process_single_message(self, message: Dict[str, Any], sqs_client):
        receipt_handle = message["ReceiptHandle"]
        try:
            body = json.loads(message["Body"])
            s3_event = json.loads(body["Message"]) if "Message" in body else body

            if "Records" not in s3_event:
                logger.warning("[Classification Worker] Invalid message, skipping.")
                await self._delete_message(receipt_handle, sqs_client)
                return

            tasks = []
            for record in s3_event["Records"]:
                bucket = record["s3"]["bucket"]["name"]
                key = record["s3"]["object"]["key"]
                logger.info(f"[Classification Worker] Queueing {key}")
                tasks.append(self._handle_ingestion(bucket, key))

            results = await asyncio.gather(*tasks, return_exceptions=False)

            if all(results):
                await self._delete_message(receipt_handle, sqs_client)
                logger.info("[Classification Worker] All ingestions succeeded, message deleted.")
            
            else:
                logger.error("[Classification Worker] Some ingestions failed — message NOT deleted, will retry.")     

        except Exception as e:
            logger.error(f"[Classification Worker] Failed message: {e}")

    async def _delete_message(self, receipt_handle: str, sqs_client):
        try:
            await sqs_client.delete_message(
                QueueUrl=self.queue_url, ReceiptHandle=receipt_handle
            )
            logger.info("[Classification Worker] Deleted message from queue.")
        except ClientError as e:
            logger.error(f"[Classification Worker] Delete failed: {e}")

    @staticmethod
    async def mock_classification(s3_uri: str) -> Dict[str, Any]:
        """
        Temporary mock function to simulate classification JSON.
        Replace this with real AI service later.
        """
        return {
            "documents": [
                {"page_no": 1, "doc_type": "bol"},
                {"page_no": 2, "doc_type": "invoice"},
                {"page_no": 3, "doc_type": "pod"}
            ]
        }

    async def _handle_ingestion(self, bucket: str, key: str):
        """
        Download PDF, classify pages, split into new PDFs, and upload back to S3.
        """
        try:
            s3_uri = f"s3://{bucket}/{key}"
            classification = await self.mock_classification(s3_uri)

            # Download original PDF
            temp_file = await s3_service.download_file(bucket, key)
            reader = PdfReader(temp_file)

            grouped_docs: Dict[str, PdfWriter] = {}

            for doc in classification["documents"]:
                page_no = doc["page_no"] - 1  # PDF pages are 0-indexed
                doc_type = doc["doc_type"]

                if doc_type not in grouped_docs:
                    grouped_docs[doc_type] = PdfWriter()

                grouped_docs[doc_type].add_page(reader.pages[page_no])

            # Upload each grouped PDF back to S3
            for doc_type, writer in grouped_docs.items():
                output_stream = BytesIO()
                writer.write(output_stream)
                output_stream.seek(0)

                path_components = key.split("/")
                if len(path_components) > 2:
                    path_after_prefix = "/".join(path_components[2:])
                else:
                    path_after_prefix = path_components[-1]

                base_path, _ = os.path.splitext(path_after_prefix)

                output_key = f"{self.output_folder}/{base_path}/{doc_type}.pdf"
                success = await s3_service.upload_file_from_bytes(
                    bucket_name=bucket,
                    object_key=output_key,
                    content_bytes=output_stream.read(),
                    content_type="application/pdf",
                )
                logger.info(f"[Classification Worker] Uploaded {output_key}")

                if not success:
                    logger.error(f"[Classification Worker] Failed to upload {output_key}")
                    return False    
                
            return True

        except Exception as e:
            logger.error(f"[Classification Worker] Ingestion failed for {key}: {e}")
            raise


if __name__ == "__main__":
    worker = DocumentClassificationProcessor(
        queue_url=settings.SQS_CLASSIFICATION_QUEUE_URL,
        region_name=settings.AWS_REGION,
    )
    asyncio.run(worker.process_messages())
