"""
Async Worker to process S3 events from an SQS queue (extraction pipeline).
"""
import sys
import json
import asyncio
from io import Bytes<PERSON>
from pathlib import Path
import os
from typing import Dict, Any

import aioboto3
from botocore.exceptions import ClientError
from loguru import logger

# Add project root to the Python path
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(project_root))

from app.core.configuration import settings
from app.services.s3_service import s3_service
from app.llm.extraction import main as llm_main


class DocumentExtractionProcessor:
    def __init__(self, queue_url: str, region_name: str):
        if not queue_url:
            raise ValueError("SQS_QUEUE_URL is required.")
        self.queue_url = queue_url
        self.region_name = region_name
        self.aws_access_key_id = settings.AWS_ACCESS_KEY_ID or None
        self.aws_secret_access_key = settings.AWS_SECRET_ACCESS_KEY or None
        self.output_folder = settings.EXTRACTION_FOLDER_OUTPUT_PREFIX
        logger.info(f"[Extraction Worker] Initialized for queue: {queue_url}")

    async def process_messages(self):
        session = aioboto3.Session()
        async with session.client(
            "sqs",
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.region_name,
        ) as sqs_client:
            while True:
                try:
                    response = await sqs_client.receive_message(
                        QueueUrl=self.queue_url,
                        MaxNumberOfMessages=10,
                        WaitTimeSeconds=20,
                        MessageAttributeNames=["All"],
                    )
                    messages = response.get("Messages", [])
                    if not messages:
                        logger.debug("[Extraction Worker] No new messages.")
                        continue

                    logger.info(f"[Extraction Worker] Got {len(messages)} messages.")
                    await asyncio.gather(
                        *[self._process_single_message(m, sqs_client) for m in messages]
                    )
                except ClientError as e:
                    logger.error(f"[Extraction Worker] SQS error: {e}")
                    await asyncio.sleep(10)

    async def _process_single_message(self, message: Dict[str, Any], sqs_client):
        receipt_handle = message["ReceiptHandle"]
        try:
            body = json.loads(message["Body"])
            s3_event = json.loads(body["Message"]) if "Message" in body else body

            if "Records" not in s3_event:
                logger.warning("[Extraction Worker] Invalid message, skipping.")
                await self._delete_message(receipt_handle, sqs_client)
                return

            tasks = []
            for record in s3_event["Records"]:
                bucket = record["s3"]["bucket"]["name"]
                key = record["s3"]["object"]["key"]
                logger.info(f"[Extraction Worker] Queueing {key}")
                tasks.append(self._handle_ingestion(bucket, key))

            await asyncio.gather(*tasks)
            await self._delete_message(receipt_handle, sqs_client)
        except Exception as e:
            logger.error(f"[Extraction Worker] Failed message: {e}")

    async def _handle_ingestion(self, bucket: str, key: str):
        try:
            s3_uri = f"s3://{bucket}/{key}"
            response = await llm_main(s3_uri=s3_uri)

            if not response:
                logger.warning(f"[Extraction Worker] Empty LLM response for {key}")
                return

            response["s3_object_key"] = key

            path_components = key.split("/")
            if len(path_components) > 2:
                path_after_prefix = "/".join(path_components[2:])
            else:
                path_after_prefix = path_components[-1]

            base_path, _ = os.path.splitext(path_after_prefix)
            output_key = f"{self.output_folder}/{base_path}.json"

            json_bytes = json.dumps(response, indent=4).encode("utf-8")
            await s3_service.upload_file_from_bytes(
                bucket_name=bucket,
                object_key=output_key,
                content_bytes=json_bytes,
                content_type="application/json",
            )

            logger.info(f"[Extraction Worker] Uploaded result to {output_key}")
        except Exception as e:
            logger.error(f"[Extraction Worker] Ingestion failed for {key}: {e}")
            raise

    async def _delete_message(self, receipt_handle: str, sqs_client):
        try:
            await sqs_client.delete_message(
                QueueUrl=self.queue_url, ReceiptHandle=receipt_handle
            )
            logger.info("[Extraction Worker] Deleted message from queue.")
        except ClientError as e:
            logger.error(f"[Extraction Worker] Delete failed: {e}")


if __name__ == "__main__":
    worker = DocumentExtractionProcessor(
        queue_url=settings.SQS_EXTRACTION_QUEUE_URL,
        region_name=settings.AWS_REGION,
    )
    asyncio.run(worker.process_messages())
